import React, {
  forwardRef,
  useImper<PERSON><PERSON><PERSON><PERSON>,
  use<PERSON><PERSON>back,
  useMemo,
  useState,
} from 'react';
import {StyleSheet, View, Alert, ScrollView} from 'react-native';
import {Text, useTheme} from 'react-native-paper';
import {
  FormProvider,
  RHFDatePicker,
  RHFSelected,
  RHFTextField,
} from '../../../../components/RHFCustomComponent';
import {useCommonProcedure} from '../../../../stores';
import {AnyObj} from '@ac-mobile/common';
import * as Yup from 'yup';
import {useForm} from 'react-hook-form';
import {yupResolver} from '@hookform/resolvers/yup';
import {
  formatDateOfBirthDisplay,
  getCitizenCode,
  getUserGender,
  getUserPhoneNumberOrEmail,
  useUserStore,
} from '../../../../stores/user.store';
import {
  ESexOptions,
  ENationalityOptions,
  EDTOptions,
  ETGOptions,
  EDocumentType,
  ENcOptions,
} from '../../../../types/informationUser';

// Improved interface definition
interface InformationUserScreenProps {}

// More descriptive ref interface
export interface InformationUserScreenRef {
  setFormData: ({key, value}: {key: string; value: AnyObj}) => void;
  submitForm: () => Promise<void>;
  isValid: () => boolean;
  isSubmitting: () => boolean;
  methods: ReturnType<typeof useForm>;
  focusFirstErrorField: (errors: any) => void;
}

// Form data interface for better type safety
interface FormData {
  FullName: string;
  Sex: SelectOption | null;
  DateOfBirth: string;
  Nationality: SelectOption;
  phoneNumber: string;
  email: string;
  DocumentType: SelectOption;
  CitizenCode: string;
  PlaceOfIssues: SelectOption;
  DateOfProvision: Date | null;
  Nation: SelectOption;
  Religion: SelectOption;
  PlaceOfOrigin: string;
  PlaceOfResidence: string;
}

interface SelectOption {
  id: string | number;
  label: string;
  value: {
    label: string;
    value: string | number;
  };
}

// Custom hook for form configuration
const useFormConfiguration = () => {
  const validationSchema = useMemo(() => {
    return Yup.object().shape({
      FullName: Yup.string()
        .transform(value => (typeof value === 'string' ? value.trim() : value))
        .required('Họ và tên không được để trống')
        .min(2, 'Họ và tên phải có ít nhất 2 ký tự'),
      Sex: Yup.object().nullable().required('Giới tính không được để trống'),
      DateOfBirth: Yup.string().required(
        'Ngày tháng năm sinh không được để trống',
      ),
      Nationality: Yup.object()
        .nullable()
        .required('Quốc tịch không được để trống'),
      phoneNumber: Yup.string()
        .transform(value => (typeof value === 'string' ? value.trim() : value))
        .required('Số điện thoại không được để trống')
        .matches(/^[0-9+\-\s()]*$/, 'Số điện thoại không hợp lệ'),
      email: Yup.string()
        .transform(value => (typeof value === 'string' ? value.trim() : value))
        .email('Email không hợp lệ'),
      DocumentType: Yup.object()
        .nullable()
        .required('Loại giấy tờ không được để trống'),
      CitizenCode: Yup.string()
        .transform(value => (typeof value === 'string' ? value.trim() : value))
        .required('Số giấy tờ không được để trống')
        .min(9, 'Số giấy tờ phải có ít nhất 9 ký tự'),
      PlaceOfIssues: Yup.object()
        .nullable()
        .required('Nơi cấp không được để trống'),
      DateOfProvision: Yup.date()
        .nullable()
        .required('Ngày cấp không được để trống')
        .max(new Date(), 'Ngày cấp không thể là ngày tương lai'),
      Nation: Yup.object().nullable().required('Dân tộc không được để trống'),
      Religion: Yup.object()
        .nullable()
        .required('Tôn giáo không được để trống'),
      PlaceOfOrigin: Yup.string()
        .transform(value => (typeof value === 'string' ? value.trim() : value))
        .required('Quê quán không được để trống')
        .min(5, 'Quê quán phải có ít nhất 5 ký tự'),
      PlaceOfResidence: Yup.string()
        .transform(value => (typeof value === 'string' ? value.trim() : value))
        .required('Nơi thường trú không được để trống')
        .min(5, 'Nơi thường trú phải có ít nhất 5 ký tự'),
    });
  }, []);

  return {validationSchema};
};

// Custom hook for default values
const useDefaultValues = () => {
  const {userProfile, getDisplayName} = useUserStore();

  return useMemo(() => {
    const gender = getUserGender(userProfile);
    const citizenCode = getCitizenCode(userProfile);
    const displayName = getDisplayName();
    const dateOfBirth = formatDateOfBirthDisplay(userProfile);
    const phoneNumber = getUserPhoneNumberOrEmail(userProfile, 'phoneNumber');
    const email = getUserPhoneNumberOrEmail(userProfile, 'email');

    const createSelectOption = (
      value: string,
      label?: string,
    ): SelectOption => ({
      id: value,
      label: label || value,
      value: {label: label || value, value},
    });

    const getSexOption = (): SelectOption | null => {
      if (gender === 0 || gender === 1) {
        const sexOption = ESexOptions[gender];
        return {
          id: gender,
          label: sexOption.label,
          value: sexOption,
        };
      }
      return null;
    };

    return {
      FullName: displayName || '',
      Sex: getSexOption(),
      DateOfBirth: dateOfBirth || '',
      Nationality: createSelectOption('Việt Nam'),
      phoneNumber: phoneNumber || '',
      email: email || '',
      DocumentType: createSelectOption('Căn cước'),
      CitizenCode: citizenCode || '',
      PlaceOfIssues: createSelectOption('Bộ công an'),
      DateOfProvision: null,
      Nation: createSelectOption('Kinh'),
      Religion: createSelectOption('Không'),
      PlaceOfOrigin: '',
      PlaceOfResidence: '',
    } as FormData;
  }, [userProfile, getDisplayName]);
};

// Utility functions for options mapping
const mapOptionsToSelectFormat = (options: any[]) =>
  options.map(opt => ({
    id: opt.value ?? opt.label,
    label: opt.label,
    value: opt,
  }));

export const InformationUserScreen = React.memo(
  forwardRef<InformationUserScreenRef, InformationUserScreenProps>(
    ({}, ref) => {
      const [isSubmitting, setIsSubmitting] = useState(false);
      const {eFormData, handleInitForm} = useCommonProcedure();
      const {userProfile} = useUserStore();
      const {validationSchema} = useFormConfiguration();
      const defaultValues = useDefaultValues();

      const methods = useForm<FormData>({
        mode: 'all',
        reValidateMode: 'onChange',
        shouldUnregister: false,
        resolver: yupResolver(validationSchema),
        defaultValues,
      });

      const {handleSubmit, setValue, formState} = methods;

      // Memoized options to prevent unnecessary re-renders
      const formOptions = useMemo(
        () => ({
          sexOptions: mapOptionsToSelectFormat(ESexOptions),
          nationalityOptions: mapOptionsToSelectFormat(ENationalityOptions),
          documentTypeOptions: mapOptionsToSelectFormat(EDocumentType),
          placeOfIssuesOptions: mapOptionsToSelectFormat(ENcOptions),
          nationOptions: mapOptionsToSelectFormat(EDTOptions),
          religionOptions: mapOptionsToSelectFormat(ETGOptions),
        }),
        [],
      );

      // --- refs for each field ---
      const refs = React.useMemo(
        () =>
          ({
            FullName: React.createRef<any>(),
            Sex: React.createRef<any>(),
            DateOfBirth: React.createRef<any>(),
            Nationality: React.createRef<any>(),
            phoneNumber: React.createRef<any>(),
            email: React.createRef<any>(),
            DocumentType: React.createRef<any>(),
            CitizenCode: React.createRef<any>(),
            PlaceOfIssues: React.createRef<any>(),
            DateOfProvision: React.createRef<any>(),
            Nation: React.createRef<any>(),
            Religion: React.createRef<any>(),
            PlaceOfOrigin: React.createRef<any>(),
            PlaceOfResidence: React.createRef<any>(),
          } as Record<string, React.RefObject<any>>),
        [],
      );
      // Ref for ScrollView
      const scrollViewRef = React.useRef<ScrollView>(null);

      const theme = useTheme();

      // Focus/open the first error field

      // Define setFormData to allow parent to set a field value
      const setFormData = useCallback(
        ({key, value}: {key: string; value: any}) => {
          // @ts-ignore
          setValue(key, value, {shouldValidate: true, shouldDirty: true});
        },
        [setValue],
      );

      // Expose imperative methods to parent (must be after all hooks and function definitions)
      useImperativeHandle(
        ref,
        () => ({
          setFormData,
          submitForm: async () => {
            await handleSubmit(handleFormSubmit)();
          },
          isValid: () => formState.isValid,
          isSubmitting: () => isSubmitting,
          methods: methods as any, // type cast to avoid type error
          focusFirstErrorField,
        }),
        [
          setFormData,
          methods,
          handleSubmit,
          formState.isValid,
          isSubmitting,
          focusFirstErrorField,
          handleFormSubmit,
        ],
      );

      // Focus/open the first error field and scroll it into view
      const focusFirstErrorField = useCallback(
        (errorsParam?: any) => {
          const errors = errorsParam || formState.errors;
          if (!errors) return;

          const errorOrder = [
            'FullName',
            'Sex',
            'DateOfBirth',
            'Nationality',
            'phoneNumber',
            'email',
            'DocumentType',
            'CitizenCode',
            'PlaceOfIssues',
            'DateOfProvision',
            'Nation',
            'Religion',
            'PlaceOfOrigin',
            'PlaceOfResidence',
          ];

          for (const key of errorOrder) {
            if (errors[key] && refs[key] && refs[key].current) {
              // Focus the field
              setTimeout(() => {
                if (typeof refs[key].current.focus === 'function') {
                  refs[key].current.focus();
                } else if (typeof refs[key].current.open === 'function') {
                  refs[key].current.open();
                }
              }, 100);

              // Scroll to the field with better positioning
              setTimeout(() => {
                if (refs[key].current && scrollViewRef.current) {
                  try {
                    const node = refs[key].current;
                    if (node && node.measureLayout) {
                      node.measureLayout(
                        scrollViewRef.current.getInnerViewNode(),
                        (
                          x: number,
                          y: number,
                          width: number,
                          height: number,
                        ) => {
                          // Scroll with more padding above the field
                          const scrollOffset = Math.max(y - 120, 0); // Increased padding
                          scrollViewRef.current?.scrollTo({
                            y: scrollOffset,
                            animated: true,
                          });
                        },
                        (error: any) => {
                          console.warn('measureLayout failed:', error);
                        },
                      );
                    }
                  } catch (error) {
                    console.warn('Focus scroll error:', error);
                  }
                }
              }, 200);
              break;
            }
          }
        },
        [formState.errors, refs],
      );

      // Handle form submit: trim all text fields before submit
      const handleFormSubmit = useCallback(
        async (data: FormData) => {
          setIsSubmitting(true);
          try {
            // Trim all text fields before submit
            const trimmedData: FormData = {
              ...data,
              FullName:
                typeof data.FullName === 'string'
                  ? data.FullName.trim()
                  : data.FullName,
              phoneNumber:
                typeof data.phoneNumber === 'string'
                  ? data.phoneNumber.trim()
                  : data.phoneNumber,
              email:
                typeof data.email === 'string' ? data.email.trim() : data.email,
              CitizenCode:
                typeof data.CitizenCode === 'string'
                  ? data.CitizenCode.trim()
                  : data.CitizenCode,
              PlaceOfOrigin:
                typeof data.PlaceOfOrigin === 'string'
                  ? data.PlaceOfOrigin.trim()
                  : data.PlaceOfOrigin,
              PlaceOfResidence:
                typeof data.PlaceOfResidence === 'string'
                  ? data.PlaceOfResidence.trim()
                  : data.PlaceOfResidence,
            };
            const ownerId = getCitizenCode(userProfile);

            if (!ownerId) {
              Alert.alert('Lỗi', 'Không tìm thấy thông tin người dùng');
              return;
            }

            if (!eFormData) {
              Alert.alert('Lỗi', 'Dữ liệu form không hợp lệ');
              return;
            }

            console.log('Submitting form data:', trimmedData);

            const payload = {
              ownerId,
              accessToken: '',
              formData: trimmedData,
            } as AnyObj;

            await handleInitForm(payload);
            Alert.alert('Thành công', 'Đã lưu thông tin thành công');
          } catch (error) {
            console.error('Form submission error:', error);
            Alert.alert('Lỗi');
          } finally {
            setIsSubmitting(false);
          }
        },
        [eFormData, handleInitForm, userProfile],
      );
      return (
        <View style={[styles.container]}>
          <View style={styles.header}>
            <Text
              variant="titleLarge"
              style={[styles.headerText, {color: theme.colors.onBackground}]}>
              Thông tin người yêu cầu
            </Text>
          </View>
          <View style={styles.formContent}>
            <FormProvider methods={methods}>
              <RHFTextField
                name="FullName"
                label="Họ và tên"
                disabled={true}
                required
                ref={refs.FullName}
              />
              <RHFSelected
                name="Sex"
                label="Giới tính"
                placeholder="Chọn giới tính"
                required
                showSearch={false}
                options={formOptions.sexOptions}
                ref={refs.Sex}
              />
              <RHFDatePicker
                name="DateOfBirth"
                label="Ngày tháng năm sinh"
                // disabled={true}
                required
                ref={refs.DateOfBirth}
              />
              <RHFSelected
                name="Nationality"
                label="Quốc tịch"
                placeholder="Chọn quốc tịch"
                required
                options={formOptions.nationalityOptions}
                ref={refs.Nationality}
              />
              <RHFTextField
                name="phoneNumber"
                label="Số điện thoại"
                keyboardType="phone-pad"
                ref={refs.phoneNumber}
                required
              />
              <RHFTextField
                name="email"
                label="Thư điện tử"
                keyboardType="email-address"
                autoCapitalize="none"
                ref={refs.email}
              />
              <RHFSelected
                name="DocumentType"
                label="Loại giấy tờ"
                placeholder="Chọn loại giấy tờ"
                required
                options={formOptions.documentTypeOptions}
                ref={refs.DocumentType}
                showSearch={false}
              />
              <RHFTextField
                name="CitizenCode"
                label="Số giấy tờ"
                disabled={true}
                required
              />
              <RHFSelected
                name="PlaceOfIssues"
                label="Nơi cấp (Nhập hoặc chọn)"
                placeholder="Nhập/chọn nơi cấp"
                required
                options={formOptions.placeOfIssuesOptions}
                ref={refs.PlaceOfIssues}
              />
              <RHFDatePicker
                name="DateOfProvision"
                label="Ngày cấp"
                max={new Date()}
                required
                ref={refs.DateOfProvision}
              />
              <RHFSelected
                name="Nation"
                label="Dân tộc"
                placeholder="Chọn dân tộc"
                required
                options={formOptions.nationOptions}
                ref={refs.Nation}
              />
              <RHFSelected
                name="Religion"
                label="Tôn giáo"
                placeholder="Chọn tôn giáo"
                required
                ref={refs.Religion}
                options={formOptions.religionOptions}
              />
              <RHFTextField
                name="PlaceOfOrigin"
                label="Quê quán"
                placeholder="Nhập quê quán"
                ref={refs.PlaceOfOrigin}
                required
              />
              <RHFTextField
                name="PlaceOfResidence"
                label="Nơi thường trú"
                placeholder="Nhập nơi thường trú"
                ref={refs.PlaceOfResidence}
                required
              />
            </FormProvider>
          </View>
        </View>
      );
    },
  ),
);

InformationUserScreen.displayName = 'InformationUserScreen';

const styles = StyleSheet.create({
  container: {
    flexGrow: 1,
    display: 'flex',
    flexDirection: 'column',
    paddingBottom: 60,
  },
  header: {
    padding: 16,
    paddingBottom: 8,
  },
  headerText: {
    fontWeight: '600',
  },
  formContent: {
    display: 'flex',
    flexDirection: 'column',
    gap: 24,
    padding: 16,
    paddingTop: 8,
  },
});
